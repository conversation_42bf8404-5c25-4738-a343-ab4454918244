package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.DailyReport;
import com.tunnel.service.DailyReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日报Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/domain/report")
public class DailyReportController extends BaseController
{
    @Autowired
    private DailyReportService dailyReportService;

    /**
     * 查询日报列表
     */
    @PreAuthorize("@ss.hasPermi('domain:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(DailyReport dailyReport)
    {
        startPage();
        List<DailyReport> list = dailyReportService.selectDailyReportList(dailyReport);
        return getDataTable(list);
    }

    /**
     * 导出日报列表
     */
    @PreAuthorize("@ss.hasPermi('domain:report:export')")
    @Log(title = "日报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DailyReport dailyReport)
    {
        List<DailyReport> list = dailyReportService.selectDailyReportList(dailyReport);
        ExcelUtil<DailyReport> util = new ExcelUtil<DailyReport>(DailyReport.class);
        util.exportExcel(response, list, "日报数据");
    }

    /**
     * 获取日报详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:report:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dailyReportService.selectDailyReportById(id));
    }

    /**
     * 新增日报
     */
    @PreAuthorize("@ss.hasPermi('domain:report:add')")
    @Log(title = "日报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DailyReport dailyReport)
    {
        return toAjax(dailyReportService.insertDailyReport(dailyReport));
    }

    /**
     * 修改日报
     */
    @PreAuthorize("@ss.hasPermi('domain:report:edit')")
    @Log(title = "日报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DailyReport dailyReport)
    {
        return toAjax(dailyReportService.updateDailyReport(dailyReport));
    }

    /**
     * 删除日报
     */
    @PreAuthorize("@ss.hasPermi('domain:report:remove')")
    @Log(title = "日报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dailyReportService.deleteDailyReportByIds(ids));
    }
}
