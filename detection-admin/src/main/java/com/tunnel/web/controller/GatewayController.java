package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Gateway;
import com.tunnel.service.GatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 远程控制网关Controller
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/detection/gateway")
public class GatewayController extends BaseController {
    @Autowired
    private GatewayService gatewayService;

    /**
     * 查询远程控制网关列表
     */
    @PostMapping("/listAll")
    @Anonymous
    public AjaxResult listAll(@RequestBody Gateway gateway) {
        List<Gateway> list = gatewayService.selectProcessingGatewayList(gateway);
        return AjaxResult.success(list);
    }

    /**
     * 导出远程控制网关列表
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:export')")
    @Log(title = "远程控制网关", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Gateway gateway) {
        List<Gateway> list = gatewayService.selectProcessingGatewayList(gateway);
        ExcelUtil<Gateway> util = new ExcelUtil<Gateway>(Gateway.class);
        util.exportExcel(response, list, "远程控制网关数据");
    }

    /**
     * 获取远程控制网关详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(gatewayService.selectProcessingGatewayById(id));
    }

    /**
     * 新增远程控制网关
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:add')")
    @Log(title = "远程控制网关", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Gateway gateway) {
        return toAjax(gatewayService.insertProcessingGateway(gateway));
    }

    /**
     * 修改远程控制网关
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:edit')")
    @Log(title = "远程控制网关", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Gateway gateway) {
        return toAjax(gatewayService.updateProcessingGateway(gateway));
    }

    /**
     * 删除远程控制网关
     */
    @PreAuthorize("@ss.hasPermi('domain:gateway:remove')")
    @Log(title = "远程控制网关", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gatewayService.deleteProcessingGatewayByIds(ids));
    }
}
