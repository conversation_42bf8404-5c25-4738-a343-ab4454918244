package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorAlarmProcessing;
import com.tunnel.service.MonitorAlarmProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测指标报警Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/domain/processing")
public class MonitorAlarmProcessingController extends BaseController
{
    @Autowired
    private MonitorAlarmProcessingService monitorAlarmProcessingService;

    /**
     * 查询监测指标报警列表
     */
    @PreAuthorize("@ss.hasPermi('domain:processing:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonitorAlarmProcessing monitorAlarmProcessing)
    {
        startPage();
        List<MonitorAlarmProcessing> list = monitorAlarmProcessingService.selectMonitorAlarmProcessingList(monitorAlarmProcessing);
        return getDataTable(list);
    }

    /**
     * 导出监测指标报警列表
     */
    @PreAuthorize("@ss.hasPermi('domain:processing:export')")
    @Log(title = "监测指标报警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitorAlarmProcessing monitorAlarmProcessing)
    {
        List<MonitorAlarmProcessing> list = monitorAlarmProcessingService.selectMonitorAlarmProcessingList(monitorAlarmProcessing);
        ExcelUtil<MonitorAlarmProcessing> util = new ExcelUtil<MonitorAlarmProcessing>(MonitorAlarmProcessing.class);
        util.exportExcel(response, list, "监测指标报警数据");
    }

    /**
     * 获取监测指标报警详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:processing:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(monitorAlarmProcessingService.selectMonitorAlarmProcessingById(id));
    }

    /**
     * 新增监测指标报警
     */
    @PreAuthorize("@ss.hasPermi('domain:processing:add')")
    @Log(title = "监测指标报警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitorAlarmProcessing monitorAlarmProcessing)
    {
        return toAjax(monitorAlarmProcessingService.insertMonitorAlarmProcessing(monitorAlarmProcessing));
    }

    /**
     * 修改监测指标报警
     */
    @PreAuthorize("@ss.hasPermi('domain:processing:edit')")
    @Log(title = "监测指标报警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitorAlarmProcessing monitorAlarmProcessing)
    {
        return toAjax(monitorAlarmProcessingService.updateMonitorAlarmProcessing(monitorAlarmProcessing));
    }

    /**
     * 删除监测指标报警
     */
    @PreAuthorize("@ss.hasPermi('domain:processing:remove')")
    @Log(title = "监测指标报警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(monitorAlarmProcessingService.deleteMonitorAlarmProcessingByIds(ids));
    }
}
