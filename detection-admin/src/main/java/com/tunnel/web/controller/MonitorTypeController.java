package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorType;
import com.tunnel.service.MonitorTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监测类型Controller
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/domain/type")
public class MonitorTypeController extends BaseController
{
    @Autowired
    private MonitorTypeService monitorTypeService;

    /**
     * 查询监测类型列表
     */
    @PreAuthorize("@ss.hasPermi('domain:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonitorType monitorType)
    {
        startPage();
        List<MonitorType> list = monitorTypeService.selectMonitorTypeList(monitorType);
        return getDataTable(list);
    }

    /**
     * 导出监测类型列表
     */
    @PreAuthorize("@ss.hasPermi('domain:type:export')")
    @Log(title = "监测类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitorType monitorType)
    {
        List<MonitorType> list = monitorTypeService.selectMonitorTypeList(monitorType);
        ExcelUtil<MonitorType> util = new ExcelUtil<MonitorType>(MonitorType.class);
        util.exportExcel(response, list, "监测类型数据");
    }

    /**
     * 获取监测类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('domain:type:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(monitorTypeService.selectMonitorTypeById(id));
    }

    /**
     * 新增监测类型
     */
    @PreAuthorize("@ss.hasPermi('domain:type:add')")
    @Log(title = "监测类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitorType monitorType)
    {
        return toAjax(monitorTypeService.insertMonitorType(monitorType));
    }

    /**
     * 修改监测类型
     */
    @PreAuthorize("@ss.hasPermi('domain:type:edit')")
    @Log(title = "监测类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitorType monitorType)
    {
        return toAjax(monitorTypeService.updateMonitorType(monitorType));
    }

    /**
     * 删除监测类型
     */
    @PreAuthorize("@ss.hasPermi('domain:type:remove')")
    @Log(title = "监测类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(monitorTypeService.deleteMonitorTypeByIds(ids));
    }
}
