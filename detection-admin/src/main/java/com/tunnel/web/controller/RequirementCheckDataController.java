package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.RequirementInfoMerge;
import com.tunnel.service.RequirementInfoMergeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

/**
 * 设备状态记录Controller
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
@Controller
@RequestMapping("/check/checkData")
public class RequirementCheckDataController extends BaseController
{

    @Resource
    private RequirementInfoMergeService requirementInfoMergeService;

    /**
     * 查询设备状态记录列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody RequirementInfoMerge requirementInfoMerge)
    {
        startPage();
        List<RequirementInfoMerge> list = requirementInfoMergeService.selectRequirementReportList(requirementInfoMerge);
        return getDataTable(list);
    }

    /**
     * 查询设备状态记录列表
     */
    @GetMapping("/{key}")
    @ResponseBody
    public TableDataInfo getByKey(@PathVariable(value = "key") String key)
    {
        RequirementInfoMerge list = requirementInfoMergeService.selectRequirementReportByKey(key);
        return getDataTable(Collections.singletonList(list));
    }

    /**
     * 导出设备状态记录列表
     */
    @Log(title = "设备状态记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public void export(RequirementInfoMerge requirementInfoMerge, HttpServletResponse response)
    {
        List<RequirementInfoMerge> list = requirementInfoMergeService.selectRequirementReportList(requirementInfoMerge);
        ExcelUtil<RequirementInfoMerge> util = new ExcelUtil<>(RequirementInfoMerge.class);
        util.exportExcel(response, list, "设备数据记录");
    }

    /**
     * 新增保存设备状态记录
     */
    @Log(title = "设备状态记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody RequirementInfoMerge requirementInfoMerge)
    {
        return toAjax(requirementInfoMergeService.insertRequirement(requirementInfoMerge));
    }

    /**
     * 修改保存设备状态记录
     */
    @Log(title = "设备状态记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestBody RequirementInfoMerge requirementInfoMerge)
    {
        return toAjax(requirementInfoMergeService.updateRequirement(requirementInfoMerge));
    }

    /**
     * 删除设备状态记录
     */
    @Log(title = "设备状态记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(@RequestParam(value = "keys") String keys)
    {
        return toAjax(requirementInfoMergeService.deleteRequirementList(keys));
    }

    /**
     * 获取属性映射
     */
    @GetMapping( "/property/mapping")
    @ResponseBody
    public TableDataInfo propertyMapping()
    {
        List<?> list = requirementInfoMergeService.getPropertyMapping();
        return getDataTable(list, (long) list.size());
    }

    @PostMapping("/importTemplate")
    @ResponseBody
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<RequirementInfoMerge> util = new ExcelUtil<>(RequirementInfoMerge.class);
        util.exportExcel(response, Collections.emptyList(), "设备数据记录模板");
    }

    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file)
    {
        try (InputStream is = file.getInputStream()) {
            ExcelUtil<RequirementInfoMerge> util = new ExcelUtil<>(RequirementInfoMerge.class);
            List<RequirementInfoMerge> list =  util.importExcel(is);
            for (RequirementInfoMerge item : list) {
                try {
                    requirementInfoMergeService.insertRequirement(item);
                } catch (Exception e) {
                    // 失败跳过
                    log.error("导入失败", e);
                }
            }
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

        return AjaxResult.success();
    }
}
