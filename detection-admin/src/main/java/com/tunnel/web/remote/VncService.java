package com.tunnel.web.remote;

import com.tunnel.web.remote.dto.UrlWrapper;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * vnc服务调用客户端
 *
 * <AUTHOR>
 */
@FeignClient(name = "vnc-service", url = "http://101.132.155.82:8091")
public interface VncService {

    @GetMapping("/api/getVNCUrlById")
    UrlWrapper getVncUrlById(@RequestParam(value = "id") String id);
}