package com.tunnel.web.remote.dto;

import lombok.Data;

/**
 * \* Created with IntelliJ IDEA.
 * \* @author: 彭诗杰
 * \* @date: 2019/12/6
 * \* @time: 16:49
 * \* Description:
 * \
 */
@Data
public class UrlResult {

    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        // 重写set方法，vnc访问的url = 西肯麦基础url + 动态获取的url
        //this.url = Constant.SECOMEA_BASEURL + "/" + url;
    }

    @Override
    public String toString() {
        return "UrlResult{" +
                "rest='" + url + '\'' +
                '}';
    }
}
