package com.tunnel.web.remote.facade;

import com.tunnel.web.remote.VncService;
import com.tunnel.web.remote.dto.UrlResult;
import com.tunnel.web.remote.dto.UrlWrapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/14  18:37
 * @since 1.0.0
 */
@Component
public class VncServiceFacade {

    @Resource
    private VncService vncService;

    public UrlResult getVncUrl(String mac) {
        UrlWrapper urlWrapper = vncService.getVncUrlById(mac);
        return urlWrapper.getResult();
    }
}
