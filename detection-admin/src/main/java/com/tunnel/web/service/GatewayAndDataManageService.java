package com.tunnel.web.service;

import com.tunnel.domain.ProcessingGateway;
import com.tunnel.domain.SecomeaField;
import com.tunnel.web.controller.dto.GatewayManageConditionDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/14  15:23
 * @since 1.0.0
 */
public interface GatewayAndDataManageService {

    List<ProcessingGateway> gatewayDownListList(GatewayManageConditionDTO gatewayManageCondition);

    List<SecomeaField> contorlPointAll(GatewayManageConditionDTO gatewayManageCondition);

    List<Map<String, Object>> controlPointRealDataList(GatewayManageConditionDTO gatewayManageCondition);
}
