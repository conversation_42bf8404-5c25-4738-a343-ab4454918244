package com.tunnel.common.utils;

import com.tunnel.common.core.domain.entity.SysRole;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据用户角色获取可查看审核状态
 *
 * <AUTHOR>
 * 2025年07月26日 12:42
 */
@Slf4j
public class CheckStatusUtil {

    /**
     * 获取当前用户可以查看的数据状态
     *
     * @return 数据状态
     */
    public static List<Integer> getUserCheckStatus() {
        // 获取用户角色
        List<String> roleList = SecurityUtils.getLoginUser().getUser().getRoles()
                .stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        log.info("当前用户角色列表：{}", roleList);
        Set<Integer> statusSet = new HashSet<>();
        // 根据角色添加对应的状态
        if (roleList.contains("JCY") || roleList.contains("XMJL")) {
            // 检测员/项目经理: 待审核/已通过/未通过
            Collections.addAll(statusSet, 0, 1, 2);
        }
        if (roleList.contains("YHJCFZR")) {
            // 养护检测负责人: 已通过
            statusSet.add(1);
        }
        log.info("当前用户可查看审核状态：{}", statusSet);
        return new ArrayList<>(statusSet);
    }

}
