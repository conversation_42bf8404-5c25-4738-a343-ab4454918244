package com.tunnel.service;

import com.tunnel.domain.MonitorStation;

import java.util.List;

/**
 * 监测站点Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorStationService
{
    /**
     * 查询监测站点
     * 
     * @param id 监测站点主键
     * @return 监测站点
     */
    public MonitorStation selectMonitorStationById(Long id);

    /**
     * 查询监测站点列表
     * 
     * @param monitorStation 监测站点
     * @return 监测站点集合
     */
    public List<MonitorStation> selectMonitorStationList(MonitorStation monitorStation);

    /**
     * 新增监测站点
     * 
     * @param monitorStation 监测站点
     * @return 结果
     */
    public int insertMonitorStation(MonitorStation monitorStation);

    /**
     * 修改监测站点
     * 
     * @param monitorStation 监测站点
     * @return 结果
     */
    public int updateMonitorStation(MonitorStation monitorStation);

    /**
     * 批量删除监测站点
     * 
     * @param ids 需要删除的监测站点主键集合
     * @return 结果
     */
    public int deleteMonitorStationByIds(Long[] ids);

    /**
     * 删除监测站点信息
     * 
     * @param id 监测站点主键
     * @return 结果
     */
    public int deleteMonitorStationById(Long id);
}
