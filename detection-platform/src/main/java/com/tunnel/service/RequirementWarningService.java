package com.tunnel.service;

import com.tunnel.domain.RequirementWarning;

import java.util.List;

/**
 * 设备报警记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface RequirementWarningService
{
    /**
     * 查询设备报警记录
     * 
     * @param id 设备报警记录主键
     * @return 设备报警记录
     */
    public RequirementWarning selectRequirementWarningById(String id);

    /**
     * 查询设备报警记录列表
     * 
     * @param requirementWarning 设备报警记录
     * @return 设备报警记录集合
     */
    public List<RequirementWarning> selectRequirementWarningList(RequirementWarning requirementWarning);

    /**
     * 新增设备报警记录
     * 
     * @param requirementWarning 设备报警记录
     * @return 结果
     */
    public int insertRequirementWarning(RequirementWarning requirementWarning);

    /**
     * 修改设备报警记录
     * 
     * @param requirementWarning 设备报警记录
     * @return 结果
     */
    public int updateRequirementWarning(RequirementWarning requirementWarning);

    /**
     * 批量删除设备报警记录
     * 
     * @param ids 需要删除的设备报警记录主键集合
     * @return 结果
     */
    public int deleteRequirementWarningByIds(String ids);

    /**
     * 删除设备报警记录信息
     * 
     * @param id 设备报警记录主键
     * @return 结果
     */
    public int deleteRequirementWarningById(String id);

    public List<RequirementWarning> listByKey(String key);
}
