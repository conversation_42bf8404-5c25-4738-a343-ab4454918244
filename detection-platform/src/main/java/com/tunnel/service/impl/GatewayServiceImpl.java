package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.Gateway;
import com.tunnel.mapper.GatewayMapper;
import com.tunnel.service.GatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 远程控制网关Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class GatewayServiceImpl implements GatewayService {
    @Autowired
    private GatewayMapper gatewayMapper;

    /**
     * 查询远程控制网关
     *
     * @param id 远程控制网关主键
     * @return 远程控制网关
     */
    @Override
    public Gateway selectProcessingGatewayById(Long id) {
        return gatewayMapper.selectProcessingGatewayById(id);
    }

    /**
     * 查询远程控制网关列表
     *
     * @param gateway 远程控制网关
     * @return 远程控制网关
     */
    @Override
    public List<Gateway> selectProcessingGatewayList(Gateway gateway) {
        return gatewayMapper.selectProcessingGatewayList(gateway);
    }

    /**
     * 新增远程控制网关
     *
     * @param gateway 远程控制网关
     * @return 结果
     */
    @Override
    public int insertProcessingGateway(Gateway gateway) {
        gateway.setCreateTime(DateUtils.getNowDate());
        return gatewayMapper.insertProcessingGateway(gateway);
    }

    /**
     * 修改远程控制网关
     *
     * @param gateway 远程控制网关
     * @return 结果
     */
    @Override
    public int updateProcessingGateway(Gateway gateway) {
        gateway.setUpdateTime(DateUtils.getNowDate());
        return gatewayMapper.updateProcessingGateway(gateway);
    }

    /**
     * 批量删除远程控制网关
     *
     * @param ids 需要删除的远程控制网关主键
     * @return 结果
     */
    @Override
    public int deleteProcessingGatewayByIds(Long[] ids) {
        return gatewayMapper.deleteProcessingGatewayByIds(ids);
    }

    /**
     * 删除远程控制网关信息
     *
     * @param id 远程控制网关主键
     * @return 结果
     */
    @Override
    public int deleteProcessingGatewayById(Long id) {
        return gatewayMapper.deleteProcessingGatewayById(id);
    }

    @Override
    public List<Gateway> selectProcessingGatewayAll() {
        return gatewayMapper.selectProcessingGatewayAll();
    }
}

