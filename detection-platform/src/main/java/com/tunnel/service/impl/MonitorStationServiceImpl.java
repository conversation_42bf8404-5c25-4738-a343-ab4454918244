package com.tunnel.service.impl;

import com.tunnel.common.utils.DateUtils;
import com.tunnel.domain.MonitorStation;
import com.tunnel.mapper.MonitorStationMapper;
import com.tunnel.service.MonitorStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监测站点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Service
public class MonitorStationServiceImpl implements MonitorStationService
{
    @Autowired
    private MonitorStationMapper monitorStationMapper;

    /**
     * 查询监测站点
     * 
     * @param id 监测站点主键
     * @return 监测站点
     */
    @Override
    public MonitorStation selectMonitorStationById(Long id)
    {
        return monitorStationMapper.selectMonitorStationById(id);
    }

    /**
     * 查询监测站点列表
     * 
     * @param monitorStation 监测站点
     * @return 监测站点
     */
    @Override
    public List<MonitorStation> selectMonitorStationList(MonitorStation monitorStation)
    {
        return monitorStationMapper.selectMonitorStationList(monitorStation);
    }

    /**
     * 新增监测站点
     * 
     * @param monitorStation 监测站点
     * @return 结果
     */
    @Override
    public int insertMonitorStation(MonitorStation monitorStation)
    {
        monitorStation.setCreateTime(DateUtils.getNowDate());
        return monitorStationMapper.insertMonitorStation(monitorStation);
    }

    /**
     * 修改监测站点
     * 
     * @param monitorStation 监测站点
     * @return 结果
     */
    @Override
    public int updateMonitorStation(MonitorStation monitorStation)
    {
        monitorStation.setUpdateTime(DateUtils.getNowDate());
        return monitorStationMapper.updateMonitorStation(monitorStation);
    }

    /**
     * 批量删除监测站点
     * 
     * @param ids 需要删除的监测站点主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationByIds(Long[] ids)
    {
        return monitorStationMapper.deleteMonitorStationByIds(ids);
    }

    /**
     * 删除监测站点信息
     * 
     * @param id 监测站点主键
     * @return 结果
     */
    @Override
    public int deleteMonitorStationById(Long id)
    {
        return monitorStationMapper.deleteMonitorStationById(id);
    }
}
