package com.tunnel.service.impl;

import com.tunnel.common.core.text.Convert;
import com.tunnel.domain.RequirementReport;
import com.tunnel.mapper.RequirementReportMapper;
import com.tunnel.mapper.RequirementStatusMapper;
import com.tunnel.mapper.RequirementWarningMapper;
import com.tunnel.service.RequirementReportService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 设备上报数据记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class RequirementReportServiceImpl implements RequirementReportService {

    @Resource
    private RequirementReportMapper requirementReportMapper;
    @Resource
    private RequirementStatusMapper requirementStatusMapper;
    @Resource
    private RequirementWarningMapper requirementWarningMapper;

    /**
     * 查询设备上报数据记录
     *
     * @param id 设备上报数据记录主键
     * @return 设备上报数据记录
     */
    @Override
    public RequirementReport selectRequirementReportById(String id) {
        return requirementReportMapper.selectRequirementReportById(id);
    }

    /**
     * 查询设备上报数据记录列表
     *
     * @param requirementReport 设备上报数据记录
     * @return 设备上报数据记录
     */
    @Override
    public List<RequirementReport> selectRequirementReportList(RequirementReport requirementReport) {
        return requirementReportMapper.selectRequirementReportList(requirementReport);
    }

    /**
     * 新增设备上报数据记录
     *
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    @Override
    public int insertRequirementReport(RequirementReport requirementReport) {
        return requirementReportMapper.insertRequirementReport(requirementReport);
    }

    /**
     * 修改设备上报数据记录
     *
     * @param requirementReport 设备上报数据记录
     * @return 结果
     */
    @Override
    public int updateRequirementReport(RequirementReport requirementReport) {
        return requirementReportMapper.updateRequirementReport(requirementReport);
    }

    /**
     * 批量删除设备上报数据记录
     *
     * @param ids 需要删除的设备上报数据记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementReportByIds(String ids) {
        return requirementReportMapper.deleteRequirementReportByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除设备上报数据记录信息
     *
     * @param id 设备上报数据记录主键
     * @return 结果
     */
    @Override
    public int deleteRequirementReportById(String id) {
        return requirementReportMapper.deleteRequirementReportById(id);
    }

    public List<RequirementReport> listByKey(String key) {
        return requirementReportMapper.listByKey(key);
    }

    @Override
    public List<Map<String, Object>> getCheckFieldsByType(Integer type) {
        List<Map<String, Object>> resultList = Lists.newArrayList();
        if (Objects.equals(type, 1)) {
            //上报数据
            resultList = requirementReportMapper.getAllFields();
        } else if (Objects.equals(type, 2)) {
            //状态数据
            resultList = requirementStatusMapper.getAllFields();
        } else if (Objects.equals(type, 3)) {
            //报警数据
            resultList = requirementWarningMapper.getAllFields();
        } else {
            return Collections.emptyList();
        }
        return resultList;
    }
}
