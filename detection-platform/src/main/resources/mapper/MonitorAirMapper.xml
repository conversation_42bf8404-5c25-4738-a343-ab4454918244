<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorAirMapper">

    <resultMap type="MonitorAir" id="ScMonitorAirResult">
        <result property="id" column="id"/>
        <result property="monitorCode" column="monitor_code"/>
        <result property="bKey" column="b_key"/>
        <result property="topic" column="topic"/>
        <result property="t" column="t"/>
        <result property="h" column="h"/>
        <result property="bp" column="bp"/>
        <result property="ws" column="ws"/>
        <result property="wd" column="wd"/>
        <result property="no" column="no"/>
        <result property="no2" column="no2"/>
        <result property="so2" column="so2"/>
        <result property="pm10_0" column="pm10_0"/>
        <result property="co" column="co"/>
        <result property="nox" column="nox"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectScMonitorAirVo">
        select id, monitor_code, b_key, topic, t, h, bp, ws, wd, no, no2, so2, pm10_0, co, nox, remark, create_time, update_time
        from sc_monitor_air
    </sql>

    <select id="selectScMonitorAirList" parameterType="MonitorAir" resultMap="ScMonitorAirResult">
        <include refid="selectScMonitorAirVo"/>
        <where>
            <if test="monitorCode != null and monitorCode != ''">
                AND monitor_code = #{monitorCode}
            </if>
            <if test="bKey != null and bKey != ''">
                AND b_key = #{bKey}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectScMonitorAirById" parameterType="Long" resultMap="ScMonitorAirResult">
        <include refid="selectScMonitorAirVo"/>
        where id = #{id}
    </select>

    <insert id="insertScMonitorAir" parameterType="MonitorAir" useGeneratedKeys="true" keyProperty="id">
        insert into sc_monitor_air
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monitorCode != null">monitor_code,</if>
            <if test="bKey != null">b_key,</if>
            <if test="topic != null">topic,</if>
            <if test="t != null">t,</if>
            <if test="h != null">h,</if>
            <if test="bp != null">bp,</if>
            <if test="ws != null">ws,</if>
            <if test="wd != null">wd,</if>
            <if test="no != null">no,</if>
            <if test="no2 != null">no2,</if>
            <if test="so2 != null">so2,</if>
            <if test="pm10_0 != null">pm10_0,</if>
            <if test="co != null">co,</if>
            <if test="nox != null">nox,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monitorCode != null">#{monitorCode},</if>
            <if test="bKey != null">#{bKey},</if>
            <if test="topic != null">#{topic},</if>
            <if test="t != null">#{t},</if>
            <if test="h != null">#{h},</if>
            <if test="bp != null">#{bp},</if>
            <if test="ws != null">#{ws},</if>
            <if test="wd != null">#{wd},</if>
            <if test="no != null">#{no},</if>
            <if test="no2 != null">#{no2},</if>
            <if test="so2 != null">#{so2},</if>
            <if test="pm10_0 != null">#{pm10_0},</if>
            <if test="co != null">#{co},</if>
            <if test="nox != null">#{nox},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into sc_monitor_air (monitor_code, b_key, topic, t, h, bp, ws, wd, no, no2, so2, pm10_0, co, nox, remark, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.monitorCode}, #{item.bKey}, #{item.topic}, #{item.t}, #{item.h}, #{item.bp}, #{item.ws}, #{item.wd}, #{item.no}, #{item.no2}, #{item.so2}, #{item.pm10_0}, #{item.co}, #{item.nox}, #{item.remark}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateScMonitorAir" parameterType="MonitorAir">
        update sc_monitor_air
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitorCode != null">monitor_code = #{monitorCode},</if>
            <if test="bKey != null">b_key = #{bKey},</if>
            <if test="topic != null">topic = #{topic},</if>
            <if test="t != null">t = #{t},</if>
            <if test="h != null">h = #{h},</if>
            <if test="bp != null">bp = #{bp},</if>
            <if test="ws != null">ws = #{ws},</if>
            <if test="wd != null">wd = #{wd},</if>
            <if test="no != null">no = #{no},</if>
            <if test="no2 != null">no2 = #{no2},</if>
            <if test="so2 != null">so2 = #{so2},</if>
            <if test="pm10_0 != null">pm10_0 = #{pm10_0},</if>
            <if test="co != null">co = #{co},</if>
            <if test="nox != null">nox = #{nox},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScMonitorAirById" parameterType="Long">
        delete from sc_monitor_air where id = #{id}
    </delete>

    <delete id="deleteScMonitorAirByIds" parameterType="Long">
        delete from sc_monitor_air where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
