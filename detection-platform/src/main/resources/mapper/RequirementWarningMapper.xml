<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.RequirementWarningMapper">
    
    <resultMap type="com.tunnel.domain.RequirementWarning" id="RequirementWarningResult">
        <result property="id"    column="id"    />
        <result property="topic"  column="topic"    />
        <result property="key"    column="b_key"    />
        <result property="gatewayId"    column="gateway_id"    />
        <result property="v910"    column="v91_0"    />
        <result property="v911"    column="v91_1"    />
        <result property="v912"    column="v91_2"    />
        <result property="v913"    column="v91_3"    />
        <result property="v914"    column="v91_4"    />
        <result property="v915"    column="v91_5"    />
        <result property="v920"    column="v92_0"    />
        <result property="v921"    column="v92_1"    />
        <result property="v922"    column="v92_2"    />
        <result property="v923"    column="v92_3"    />
        <result property="v924"    column="v92_4"    />
        <result property="v925"    column="v92_5"    />
        <result property="v926"    column="v92_6"    />
        <result property="v927"    column="v92_7"    />
        <result property="v930"    column="v93_0"    />
        <result property="v931"    column="v93_1"    />
        <result property="v932"    column="v93_2"    />
        <result property="v933"    column="v93_3"    />
        <result property="v934"    column="v93_4"    />
        <result property="v935"    column="v93_5"    />
         <result property="v936"    column="v93_6"    />
         <result property="v937"    column="v93_7"    />
         <result property="v940"    column="v94_0"    />
         <result property="v942"    column="v94_2"    />
        <result property="v950"    column="v95_0"    />
        <result property="v951"    column="v95_1"    />
        <result property="v952"    column="v95_2"    />
        <result property="v953"    column="v95_3"    />
        <result property="v916"    column="v91_6"    />
        <result property="v917"    column="v91_7"    />
        <result property="v954"    column="v95_4"    />
        <result property="v955"    column="v95_5"    />
        <result property="v956"    column="v95_6"    />
         <result property="v957"    column="v95_7"    />
         <result property="v960"    column="v96_0"    />
         <result property="dcmAlarm"    column="dcm_alarm"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="selectRequirementWarningVo">
        select id, b_key, topic, gateway_id, v91_0, v91_1, v91_2, v91_3, v91_4, v91_5, v92_0, v92_1, v92_2, v92_3, v92_4, v92_5, v92_6, v92_7, v93_0, v93_1, v93_2, v93_3, v93_4, v93_5, v93_6, v93_7, v94_0, v94_2, v95_0, v95_1, v95_2, v95_3, v91_6, v91_7, v95_4, v95_5, v95_6, v95_7, v96_0, dcm_alarm,
               remark, create_time, update_time, creator, modifier
        from sc_requirement_warning
    </sql>

    <select id="selectRequirementWarningList" parameterType="com.tunnel.domain.RequirementWarning" resultMap="RequirementWarningResult">
        <include refid="selectRequirementWarningVo"/>
        <where>  
            <if test="key != null  and key != ''"> and b_key = #{key}</if>
            <if test="topic != null  and topic != ''"> and topic = #{topic}</if>
            <if test="v910 != null  and v910 != ''"> and v91_0 = #{v910}</if>
            <if test="v911 != null  and v911 != ''"> and v91_1 = #{v911}</if>
            <if test="v912 != null  and v912 != ''"> and v91_2 = #{v912}</if>
            <if test="v913 != null  and v913 != ''"> and v91_3 = #{v913}</if>
            <if test="v914 != null  and v914 != ''"> and v91_4 = #{v914}</if>
            <if test="v915 != null  and v915 != ''"> and v91_5 = #{v915}</if>
            <if test="v920 != null  and v920 != ''"> and v92_0 = #{v920}</if>
            <if test="v921 != null  and v921 != ''"> and v92_1 = #{v921}</if>
            <if test="v922 != null  and v922 != ''"> and v92_2 = #{v922}</if>
            <if test="v923 != null  and v923 != ''"> and v92_3 = #{v923}</if>
            <if test="v924 != null  and v924 != ''"> and v92_4 = #{v924}</if>
            <if test="v925 != null  and v925 != ''"> and v92_5 = #{v925}</if>
            <if test="v926 != null  and v926 != ''"> and v92_6 = #{v926}</if>
            <if test="v927 != null  and v927 != ''"> and v92_7 = #{v927}</if>
            <if test="v930 != null  and v930 != ''"> and v93_0 = #{v930}</if>
            <if test="v931 != null  and v931 != ''"> and v93_1 = #{v931}</if>
            <if test="v932 != null  and v932 != ''"> and v93_2 = #{v932}</if>
            <if test="v933 != null  and v933 != ''"> and v93_3 = #{v933}</if>
            <if test="v934 != null  and v934 != ''"> and v93_4 = #{v934}</if>
            <if test="v935 != null  and v935 != ''"> and v93_5 = #{v935}</if>
            <if test="v936 != null  and v936 != ''"> and v93_6 = #{v936}</if>
            <if test="v937 != null  and v937 != ''"> and v93_7 = #{v937}</if>
            <if test="v950 != null  and v950 != ''"> and v95_0 = #{v950}</if>
            <if test="v951 != null  and v951 != ''"> and v95_1 = #{v951}</if>
            <if test="v952 != null  and v952 != ''"> and v95_2 = #{v952}</if>
            <if test="v953 != null  and v953 != ''"> and v95_3 = #{v953}</if>
            <if test="v916 != null  and v916 != ''"> and v91_6 = #{v916}</if>
            <if test="v917 != null  and v917 != ''"> and v91_7 = #{v917}</if>
            <if test="v954 != null  and v954 != ''"> and v95_4 = #{v954}</if>
            <if test="v955 != null  and v955 != ''"> and v95_5 = #{v955}</if>
            <if test="v956 != null  and v956 != ''"> and v95_6 = #{v956}</if>
        </where>
    </select>
    
    <select id="selectRequirementWarningById" parameterType="String" resultMap="RequirementWarningResult">
        <include refid="selectRequirementWarningVo"/>
        where id = #{id}
    </select>

    <insert id="insertRequirementWarning" parameterType="com.tunnel.domain.RequirementWarning" useGeneratedKeys="true" keyProperty="id">
        insert into sc_requirement_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="key != null and key != ''">b_key,</if>
            <if test="topic != null  and topic != ''">topic,</if>
            <if test="gatewayId != null  and gatewayId != ''">gateway_id,</if>
            <if test="v910 != null">v91_0,</if>
            <if test="v911 != null">v91_1,</if>
            <if test="v912 != null">v91_2,</if>
            <if test="v913 != null">v91_3,</if>
            <if test="v914 != null">v91_4,</if>
            <if test="v915 != null">v91_5,</if>
            <if test="v920 != null">v92_0,</if>
            <if test="v921 != null">v92_1,</if>
            <if test="v922 != null">v92_2,</if>
            <if test="v923 != null">v92_3,</if>
            <if test="v924 != null">v92_4,</if>
            <if test="v925 != null">v92_5,</if>
            <if test="v926 != null">v92_6,</if>
            <if test="v927 != null">v92_7,</if>
            <if test="v930 != null">v93_0,</if>
            <if test="v931 != null">v93_1,</if>
            <if test="v932 != null">v93_2,</if>
            <if test="v933 != null">v93_3,</if>
            <if test="v934 != null">v93_4,</if>
            <if test="v935 != null">v93_5,</if>
            <if test="v936 != null">v93_6,</if>
            <if test="v937 != null">v93_7,</if>
            <if test="v940 != null">v94_0,</if>
            <if test="v942 != null">v94_2,</if>
            <if test="v950 != null">v95_0,</if>
            <if test="v951 != null">v95_1,</if>
            <if test="v952 != null">v95_2,</if>
            <if test="v953 != null">v95_3,</if>
            <if test="v916 != null">v91_6,</if>
            <if test="v917 != null">v91_7,</if>
            <if test="v954 != null">v95_4,</if>
            <if test="v955 != null">v95_5,</if>
            <if test="v956 != null">v95_6,</if>
            <if test="v957 != null">v95_7,</if>
            <if test="v960 != null">v96_0,</if>
            <if test="dcmAlarm != null">dcm_alarm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="key != null and key != ''">#{key},</if>
            <if test="topic != null  and topic != ''">#{topic},</if>
            <if test="gatewayId != null  and gatewayId != ''">#{gatewayId},</if>
            <if test="v910 != null">#{v910},</if>
            <if test="v911 != null">#{v911},</if>
            <if test="v912 != null">#{v912},</if>
            <if test="v913 != null">#{v913},</if>
            <if test="v914 != null">#{v914},</if>
            <if test="v915 != null">#{v915},</if>
            <if test="v920 != null">#{v920},</if>
            <if test="v921 != null">#{v921},</if>
            <if test="v922 != null">#{v922},</if>
            <if test="v923 != null">#{v923},</if>
            <if test="v924 != null">#{v924},</if>
            <if test="v925 != null">#{v925},</if>
            <if test="v926 != null">#{v926},</if>
            <if test="v927 != null">#{v927},</if>
            <if test="v930 != null">#{v930},</if>
            <if test="v931 != null">#{v931},</if>
            <if test="v932 != null">#{v932},</if>
            <if test="v933 != null">#{v933},</if>
            <if test="v934 != null">#{v934},</if>
            <if test="v935 != null">#{v935},</if>
            <if test="v936 != null">#{v936},</if>
            <if test="v937 != null">#{v937},</if>
            <if test="v940 != null">#{v940},</if>
            <if test="v942 != null">#{v942},</if>
            <if test="v950 != null">#{v950},</if>
            <if test="v951 != null">#{v951},</if>
            <if test="v952 != null">#{v952},</if>
            <if test="v953 != null">#{v953},</if>
            <if test="v916 != null">#{v916},</if>
            <if test="v917 != null">#{v917},</if>
            <if test="v954 != null">#{v954},</if>
            <if test="v955 != null">#{v955},</if>
            <if test="v956 != null">#{v956},</if>
            <if test="v957 != null">#{v957},</if>
            <if test="v960 != null">#{v960},</if>
            <if test="dcmAlarm != null">#{dcmAlarm},</if>
         </trim>
    </insert>

    <insert id="insertBatch">
        insert into sc_requirement_warning (
            b_key, topic, gateway_id,
            v91_0, v91_1, v91_2, v91_3, v91_4, v91_5,
            v92_0, v92_1, v92_2, v92_3, v92_4, v92_5, v92_6, v92_7,
            v93_0, v93_1, v93_2, v93_3, v93_4, v93_5, v93_6, v93_7,
            v95_0, v95_1, v95_2, v95_3, v91_6, v91_7, v95_4, v95_5, v95_6,create_time
        ) values
        <foreach collection="list" item="it" separator=",">
            (
                #{it.key}, #{it.topic}, #{it.gatewayId},
                #{it.v910}, #{it.v911}, #{it.v912}, #{it.v913}, #{it.v914}, #{it.v915},
                #{it.v920}, #{it.v921}, #{it.v922}, #{it.v923}, #{it.v924}, #{it.v925}, #{it.v926}, #{it.v927},
                #{it.v930}, #{it.v931}, #{it.v932}, #{it.v933}, #{it.v934}, #{it.v935}, #{it.v936}, #{it.v937},
                #{it.v950}, #{it.v951}, #{it.v952}, #{it.v953}, #{it.v916}, #{it.v917}, #{it.v954}, #{it.v955}, #{it.v956}, #{it.createTime}
            )
        </foreach>
    </insert>

    <update id="updateRequirementWarning" parameterType="com.tunnel.domain.RequirementWarning">
        update sc_requirement_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="key != null and key != ''">b_key = #{key},</if>
            <if test="topic != null  and topic != ''"> topic = #{topic},</if>
            <if test="v910 != null">v91_0 = #{v910},</if>
            <if test="v911 != null">v91_1 = #{v911},</if>
            <if test="v912 != null">v91_2 = #{v912},</if>
            <if test="v913 != null">v91_3 = #{v913},</if>
            <if test="v914 != null">v91_4 = #{v914},</if>
            <if test="v915 != null">v91_5 = #{v915},</if>
            <if test="v920 != null">v92_0 = #{v920},</if>
            <if test="v921 != null">v92_1 = #{v921},</if>
            <if test="v922 != null">v92_2 = #{v922},</if>
            <if test="v923 != null">v92_3 = #{v923},</if>
            <if test="v924 != null">v92_4 = #{v924},</if>
            <if test="v925 != null">v92_5 = #{v925},</if>
            <if test="v926 != null">v92_6 = #{v926},</if>
            <if test="v927 != null">v92_7 = #{v927},</if>
            <if test="v930 != null">v93_0 = #{v930},</if>
            <if test="v931 != null">v93_1 = #{v931},</if>
            <if test="v932 != null">v93_2 = #{v932},</if>
            <if test="v933 != null">v93_3 = #{v933},</if>
            <if test="v934 != null">v93_4 = #{v934},</if>
            <if test="v935 != null">v93_5 = #{v935},</if>
            <if test="v936 != null">v93_6 = #{v936},</if>
            <if test="v937 != null">v93_7 = #{v937},</if>
            <if test="v940 != null">v94_0 = #{v940},</if>
            <if test="v942 != null">v94_2 = #{v942},</if>
            <if test="v950 != null">v95_0 = #{v950},</if>
            <if test="v951 != null">v95_1 = #{v951},</if>
            <if test="v952 != null">v95_2 = #{v952},</if>
            <if test="v953 != null">v95_3 = #{v953},</if>
            <if test="v916 != null">v91_6 = #{v916},</if>
            <if test="v917 != null">v91_7 = #{v917},</if>
            <if test="v954 != null">v95_4 = #{v954},</if>
            <if test="v955 != null">v95_5 = #{v955},</if>
            <if test="v956 != null">v95_6 = #{v956},</if>
            <if test="v957 != null">v95_7 = #{v957},</if>
            <if test="v960 != null">v96_0 = #{v960},</if>
            <if test="dcmAlarm != null">dcm_alarm = #{dcmAlarm},</if>
            <choose>
                <when test="updateTime != null">update_time = #{updateTime},</when>
                <otherwise>update_time = now(),</otherwise>
            </choose>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRequirementWarningById" parameterType="String">
        delete from sc_requirement_warning where id = #{id}
    </delete>

    <delete id="deleteRequirementWarningByIds" parameterType="String">
        delete from sc_requirement_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="listByKey" resultMap="RequirementWarningResult">
        <include refid="selectRequirementWarningVo"/>
        where b_key = #{key}
    </select>
</mapper>